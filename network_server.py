#!/usr/bin/env python3
"""
خادم شبكة مخصص لـ SmartPOS
يحل مشاكل الوصول من الشبكة المحلية
"""

import http.server
import socketserver
import socket
import subprocess
import threading
import time
import os

class NetworkHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # إعادة توجيه جميع الطلبات إلى Vite
        import urllib.request
        import urllib.error
        
        try:
            # بناء URL للخادم المحلي
            vite_url = f"http://localhost:5175{self.path}"
            
            # إرسال الطلب إلى Vite
            req = urllib.request.Request(vite_url)
            
            # نسخ headers من الطلب الأصلي
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'connection']:
                    req.add_header(header, value)
            
            # إرسال الطلب
            with urllib.request.urlopen(req) as response:
                # إعداد headers للاستجابة
                self.send_response(response.getcode())
                
                # نسخ headers من Vite
                for header, value in response.headers.items():
                    if header.lower() not in ['connection', 'transfer-encoding']:
                        self.send_header(header, value)
                
                # إضافة CORS headers
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
                
                self.end_headers()
                
                # نسخ المحتوى
                content = response.read()
                self.wfile.write(content)
                
        except urllib.error.URLError as e:
            # إذا فشل الاتصال بـ Vite، أرسل رسالة خطأ
            self.send_response(503)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()
            
            error_html = f"""
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>خطأ في الاتصال</title>
                <style>
                    body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; }}
                    .error {{ color: red; font-size: 24px; }}
                </style>
            </head>
            <body>
                <h1 class="error">⚠️ خطأ في الاتصال</h1>
                <p>لا يمكن الاتصال بخادم Vite على المنفذ 5175</p>
                <p>تأكد من تشغيل: <code>npm run dev</code></p>
                <p>الخطأ: {str(e)}</p>
                <button onclick="location.reload()">إعادة المحاولة</button>
            </body>
            </html>
            """
            self.wfile.write(error_html.encode('utf-8'))
    
    def do_POST(self):
        self.do_GET()
    
    def do_PUT(self):
        self.do_GET()
    
    def do_DELETE(self):
        self.do_GET()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def check_vite_running():
    """تحقق من أن Vite يعمل"""
    try:
        import urllib.request
        urllib.request.urlopen("http://localhost:5175", timeout=2)
        return True
    except:
        return False

def main():
    PORT = 6175  # منفذ مختلف لتجنب التضارب
    local_ip = get_local_ip()
    
    print("🚀 خادم SmartPOS الشبكي")
    print("=" * 50)
    
    # تحقق من Vite
    if check_vite_running():
        print("✅ Vite يعمل على المنفذ 5175")
    else:
        print("❌ Vite لا يعمل! شغل: npm run dev")
        print("⏳ انتظار تشغيل Vite...")
        
        # انتظار تشغيل Vite
        while not check_vite_running():
            time.sleep(2)
        print("✅ Vite يعمل الآن!")
    
    # تشغيل الخادم
    with socketserver.TCPServer(("0.0.0.0", PORT), NetworkHandler) as httpd:
        print(f"\n🌐 الخادم يعمل على:")
        print(f"   المحلي: http://localhost:{PORT}")
        print(f"   الشبكة: http://{local_ip}:{PORT}")
        print(f"\n📱 استخدم هذا الرابط من الجهاز الآخر:")
        print(f"   http://{local_ip}:{PORT}")
        print(f"   http://{local_ip}:{PORT}/login")
        print(f"\n⏹️  اضغط Ctrl+C للإيقاف")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف الخادم")

if __name__ == "__main__":
    main()
