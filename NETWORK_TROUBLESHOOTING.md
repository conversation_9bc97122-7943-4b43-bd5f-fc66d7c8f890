# دليل استكشاف أخطاء الشبكة - SmartPOS

## المشكلة: لا يمكن الوصول للتطبيق عبر IP الشبكة

### 🔍 خطوات التشخيص

#### 1. تحقق من تشغيل الخادمين
```bash
# تحقق من Backend
curl http://*************:8002/docs

# تحقق من Frontend  
curl http://*************:5175
```

#### 2. تحقق من المنافذ
```bash
# تحقق من المنافذ المفتوحة
netstat -tlnp | grep -E "(5175|8002)"
```

#### 3. اختبار الاتصال
```bash
# شغل خادم الاختبار
python3 test_server.py

# ثم جرب الوصول من الجهاز الآخر إلى:
# http://*************:8080
```

### 🛠️ الحلول المحتملة

#### الحل 1: إعادة تشغيل الخادمين
```bash
# أوقف الخادمين
pkill -f "python main.py"
pkill -f "vite"

# شغل Backend
cd backend && python main.py

# شغل Frontend (في terminal آخر)
cd frontend && npm run dev
```

#### الحل 2: استخدام IP مختلف
```bash
# اكتشف جميع عناوين IP
ip addr show | grep "inet "

# جرب عناوين مختلفة مثل:
# http://********:5175
# http://localhost:5175
```

#### الحل 3: تعطيل الجدار الناري مؤقتاً
```bash
# تحقق من حالة الجدار الناري
sudo ufw status

# تعطيل مؤقت (للاختبار فقط)
sudo ufw disable
```

#### الحل 4: استخدام منفذ مختلف
```bash
# شغل Frontend على منفذ مختلف
cd frontend && npx vite --host 0.0.0.0 --port 3000
```

### 🌐 اختبار من الجهاز الآخر

#### خطوات الاختبار:
1. **افتح المتصفح** في الجهاز الآخر
2. **امسح cache المتصفح** (Ctrl+Shift+Delete)
3. **جرب العناوين التالية بالترتيب**:
   - `http://*************:8080` (خادم الاختبار)
   - `http://*************:5175` (SmartPOS)
   - `http://*************:5175/login`

#### إذا لم يعمل خادم الاختبار:
- 🔥 **مشكلة في الشبكة أو الجدار الناري**
- 🔧 **تحقق من إعدادات الراوتر**
- 📱 **تأكد من أن الأجهزة على نفس الشبكة**

#### إذا عمل خادم الاختبار ولم يعمل SmartPOS:
- ⚙️ **مشكلة في إعدادات Vite**
- 🔄 **أعد تشغيل Frontend**
- 🧹 **امسح cache المتصفح**

### 📋 معلومات النظام

#### عناوين IP الحالية:
- **المحلي**: `127.0.0.1`
- **الشبكة الرئيسية**: `*************`
- **الشبكة الفرعية**: `********`

#### المنافذ المستخدمة:
- **Backend**: `8002`
- **Frontend**: `5175`
- **خادم الاختبار**: `8080`

#### الخادمين يعملان على:
- **Backend**: `http://0.0.0.0:8002`
- **Frontend**: `http://0.0.0.0:5175`

### 🆘 إذا لم تنجح الحلول

#### جرب هذه البدائل:
1. **استخدم VPN أو Hotspot**
2. **شغل التطبيق محلياً على الجهاز الآخر**
3. **استخدم Remote Desktop**
4. **تحقق من إعدادات الراوتر (Port Forwarding)**

### 📞 للدعم الفني
إذا استمرت المشكلة، أرسل هذه المعلومات:
- نتيجة `curl http://*************:8080`
- نتيجة `curl http://*************:5175`
- رسالة الخطأ في المتصفح
- نوع المتصفح ونظام التشغيل للجهاز الآخر
