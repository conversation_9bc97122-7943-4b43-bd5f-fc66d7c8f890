import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios';
import { useAuthStore } from '../stores/authStore';

interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
  _refreshing?: boolean;
}

// Dynamic backend URL configuration for network access
const getBackendURL = () => {
  // Get current host information
  const currentHost = window.location.hostname;
  const currentPort = window.location.port;

  console.log('Current host:', currentHost, 'Port:', currentPort);

  // Check if we're in development mode
  if ((import.meta as any).env?.DEV) {
    // If accessing from localhost or 127.0.0.1, use localhost for backend
    if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
      console.log('Using localhost backend URL');
      return 'http://localhost:8002';
    }

    // If accessing from network IP, use the same IP for backend
    const backendURL = `http://${currentHost}:8002`;
    console.log('Using network backend URL:', backendURL);
    return backendURL;
  }

  // In production, use relative URLs or environment variable
  return (import.meta as any).env?.VITE_API_URL || 'http://localhost:8002';
};

// التأكد من استخدام العنوان الصحيح للخادم الخلفي
const backendURL = getBackendURL();
console.log('Initializing axios with backend URL:', backendURL);

const api = axios.create({
  baseURL: backendURL, // تعيين عنوان الخادم الخلفي ديناميكياً
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 15000, // زيادة مهلة الاتصال إلى 15 ثانية للشبكة
  withCredentials: true, // Set to true to allow credentials
});

// Keep track of if we're refreshing the token
let isRefreshing = false;
// Queue of requests to retry after token refresh
let failedQueue: Array<{
  resolve: (value?: unknown) => void;
  reject: (reason?: any) => void;
}> = [];

const processQueue = (error: any = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve();
    }
  });
  failedQueue = [];
};

// Request interceptor
api.interceptors.request.use(
  (config: CustomAxiosRequestConfig) => {
    const authStore = useAuthStore.getState();
    const token = authStore.token;

    // Don't add token for refresh token requests
    if (token && !config.headers._refreshing) {
      // Make sure we're using the latest token
      config.headers.Authorization = `Bearer ${token}`;
      console.log('Adding token to request:', config.url);
    } else {
      console.log('No token available for request:', config.url);
    }

    // Add timestamp to prevent caching
    if (config.method?.toLowerCase() === 'get') {
      config.params = {
        ...config.params,
        _t: new Date().getTime()
      };
    }

    // No need to fix URLs anymore as we're using direct backend URL

    // Debug information for requests (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.log(`${config.method?.toUpperCase()} ${config.url}`);
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error: AxiosError) => {
    console.error('Axios error:', error.message, 'Code:', error.code, 'URL:', error.config?.url);

    // Check for network errors
    if (error.code === 'ERR_NETWORK') {
      console.error('Network error detected. Backend might be down or unreachable.');
      console.error('Current backend URL:', getBackendURL());
    }

    const originalRequest = error.config as CustomAxiosRequestConfig;



    if (!originalRequest) {
      return Promise.reject(error);
    }

    // Skip refresh for login and refresh endpoints
    if (originalRequest.url?.includes('/auth/token') ||
        originalRequest.url?.includes('/auth/refresh')) {
      return Promise.reject(error);
    }

    // If we're already refreshing, queue this request
    if (error.response?.status === 401 && isRefreshing) {
      return new Promise((resolve, reject) => {
        failedQueue.push({ resolve, reject });
      })
        .then(() => {
          return api(originalRequest);
        })
        .catch(err => {
          return Promise.reject(err);
        });
    }

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      isRefreshing = true;

      console.log('Received 401 error for URL:', originalRequest.url);
      console.log('Attempting to refresh token...');

      try {
        const authStore = useAuthStore.getState();

        // Try to refresh the token
        console.log('Calling refreshAccessToken...');
        const newToken = await authStore.refreshAccessToken();
        console.log('Token refreshed successfully');

        // Update the failed request with new token
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        console.log('Updated request headers with new token');

        // Reset refreshing state
        isRefreshing = false;

        // Process queued requests
        processQueue(null);

        // Retry the original request
        console.log('Retrying original request...');
        return api(originalRequest);
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);

        // Reset refreshing state
        isRefreshing = false;
        processQueue(refreshError);

        // If we're not on the login page, redirect
        if (!window.location.pathname.includes('/login')) {
          // Clear auth state and redirect
          const authStore = useAuthStore.getState();
          authStore.logout();

          // Add a small delay before redirecting to ensure logout completes
          setTimeout(() => {
            window.location.href = '/login';
          }, 100);
        }

        return Promise.reject(refreshError);
      }
    }



    return Promise.reject(error);
  }
);

// Test backend connectivity
export const testBackendConnection = async () => {
  try {
    const response = await fetch(`${getBackendURL()}/docs`, {
      method: 'GET',
      mode: 'cors'
    });
    console.log('Backend connection test:', response.status === 200 ? 'SUCCESS' : 'FAILED');
    return response.status === 200;
  } catch (error) {
    console.error('Backend connection test FAILED:', error);
    return false;
  }
};

// Test connection on initialization
testBackendConnection();

export default api;