from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
from pathlib import Path
from routers import auth, users, products, sales_router, dashboard_router, settings_router
from routers.customers import router as customers_router
from routers.debts import router as debts_router
from database.session import engine, Base

# Create database tables
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="SmartPOS API",
    description="Backend API for SmartPOS system",
    version="1.0.0"
)

# CORS configuration for development and network access
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins in development
    allow_origin_regex=r"http://(localhost|127\.0\.0\.1|192\.168\.\d+\.\d+|10\.\d+\.\d+\.\d+|172\.16\.\d+\.\d+):(3000|5174|5175)",
    allow_credentials=True,  # Set to True to allow credentials
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],  # Explicitly list methods
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "X-Total-Count",
        "X-Page",
        "X-Limit",
        "X-Pages"
    ],
    expose_headers=["X-Total-Count", "X-Page", "X-Limit", "X-Pages"],  # Expose pagination headers
)

# Add explicit OPTIONS handler for CORS preflight requests
@app.options("/{path:path}")
async def options_handler(request: Request):
    """Handle CORS preflight requests"""
    return Response(
        status_code=200,
        headers={
            "Access-Control-Allow-Origin": request.headers.get("origin", "*"),
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, PATCH",
            "Access-Control-Allow-Headers": "Accept, Accept-Language, Content-Language, Content-Type, Authorization, X-Requested-With, X-Total-Count, X-Page, X-Limit, X-Pages",
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Max-Age": "86400"  # Cache preflight for 24 hours
        }
    )

# Include routers
app.include_router(auth)
app.include_router(users)
app.include_router(products)
app.include_router(sales_router)
app.include_router(dashboard_router)
app.include_router(settings_router)
app.include_router(customers_router)
app.include_router(debts_router)

# Serve static files
static_path = Path("static")
static_path.mkdir(exist_ok=True)
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/")
async def root():
    return {"message": "SmartPOS API is running"}

if __name__ == "__main__":
    # Use 0.0.0.0 to allow access from other devices on the network
    uvicorn.run("main:app", host="0.0.0.0", port=8002, reload=True)